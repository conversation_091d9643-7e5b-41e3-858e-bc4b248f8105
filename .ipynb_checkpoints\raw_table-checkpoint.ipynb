{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["cluster = \"az-eastus-1\"\n", "base_url = f\"https://{cluster}.cognitedata.com\"\n", "tenant_id = \"7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37\"\n", "client_id = \"cb909a16-1087-4e06-9d11-8dfbd6ae1a5c\"\n", "client_secret=\"****************************************\"\n", "project=\"celanese-dev\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from cognite.client.credentials import OAuthInteractive, OAuthClientCredentials\n", "from cognite.client import CogniteClient, ClientConfig\n", "import ssl\n", "import pandas as pd\n", "import numpy as np\n", "from cognite.client import CogniteClient\n", "from cognite.client.data_classes import Row\n", "\n", "ssl.SSLContext.verify_mode = ssl.VerifyMode.CERT_OPTIONAL"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["credentials = OAuthClientCredentials(\n", "  token_url=f\"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token\",\n", "  client_id=client_id,\n", "  client_secret=client_secret,\n", "  scopes=[f\"{base_url}/.default\"]\n", ")\n", "config = ClientConfig(\n", "    client_name=\"<PERSON><PERSON><PERSON>\",\n", "    project=project,\n", "    base_url=base_url,\n", "    credentials=credentials,\n", ")\n", "client = CogniteClient(config)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'LAST UPDATE': '22-NOV-24 01:07:21.3', 'DESCRIPTION': '2EXT Extr. Screw Speed Actual', 'NEG ROC LIM': '', 'TREND VIEW TIME': '+004:00:00.0', 'QUALITY': 'Good', 'RATE OF CHANGE': '0.*****************', 'IP ARCHIVE': 'ON', 'SIGNIFICANCE VALUE': '3.0', 'GRAPH MINIMUM': '0.0', 'IP RAW REPOSITORY': 'R200His1', 'IP HISTORY STATUS': 'OK', 'UNIT': 'SPIN02', 'AbsPath': '\\\\FILAMENTS SYSTEM\\\\SPIN02 UNIT\\\\2EXT 40517', 'ROC DEADBAND': '', 'source': 'IP21', 'ACTG RECORD': '', 'NAME': '2EXT 40517', 'LOW  LOW': '', 'AREA': 'PBRUSH', 'VALUE': '-0.*****************', 'IP REPOSITORY': 'C200His1', 'UNITS RECORD': 'PBRUSH-UNITS', 'IP RAW HISTORY STAT': 'OK', 'ACCOUNTING?': 'NO', 'LIM DEADBAND': '', 'LAST RECORDING TIME': '22-NOV-24 00:27:29.8', 'IP RAW ARCHIVING': 'ON', 'MAX TIME INTERVAL': '+001:00:00.0', 'LAST RECORDING': '-0.*****************', 'ENG UNITS': 'RPM', 'MESSAGE SW.': 'OFF', 'VALUE FORMAT': 'F 8. 2', 'NUMBER OF TREND VALS': '     2', 'DOMAIN': 'SPIN02', 'HIGH': '', 'LOW': '', 'ROC STATE': 'OK', 'ACKNOWLEDGEMENT': 'ACK', 'HIST SEQUENCE NUMBER': '    7647286', 'LAST RECORDING QUAL': 'Good', 'DCOM SEQUENCE NUMBER': '    3070890', 'POS ROC LIM': '', 'NUMBER OF DCOM VALS': '     2', 'GRAPH MAXIMUM': '125.0', 'HIGH HIGH': '', 'COMPRESSION STATUS': 'Slope', 'ALARM STATE': 'OK'}\n"]}], "source": ["# table_df = client.raw.rows.retrieve_dataframe(\"SQRE-COR-FDL-KPI\", \"PLANT-CASH-MARGIN\", limit=-1)\n", "# row_list = client.raw.rows.list(\"SQRE-COR-FDL-KPI\", \"PLANT-CASH-MARGIN\", limit=-1)\n", "res = client.time_series.retrieve(external_id=\"WWPMCFIL1:2EXT 40517\")\n", "print(res.metadata)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'listSourceEvent': {'pageInfo': {'hasNextPage': True, 'hasPreviousPage': False, 'endCursor': 'sekVWeMT0iXnNt0nUf1LObsc0QIASRxrH3pZh8fypR-9waVBX-Yz8x94pTsdsJtz8DVf_LZs6gfrXtOzWCo33efCUZsWlbK6Wkjt2OIq9Xw49h8AvJGSrEmEpU0bqkudlTjb3_ZSZhCfL80NAjZS_zjFdU0mWMqEYyJsRk986G8qvVg82lulYcO-Lhtam48N4hqW_mcToxwSFHJWyIBl1Ew4dTRBoWgoKpmx00ITI-O1_HhqXaTj4Pxmh72-7YdWk95d5cnpb21PEGWIuJYVVQmH49cjQYeU8etEhuAX0vZ8pH5vVEV_U4bdUgzsr2Ag3eWvUvLaVswqmA7dciglOq58YDZIOK0fT2y7yvCW5ShY_o48wh4xNrhGKg5_qaHQcxMbRTR4tUK0FJMTKgV6ObvdqNO2KG62wbLCJN-TvhnhqwoFFDaZbkYKN2TC2AX4E1IaSv4tAWXxUeAHcdbMKvPtYSMRtMHXUn4h4D_pH0vONo7NTgtjpXw3Sytz0RZNLp7XeCwRuD8hzHrixoxSeg=='}, 'items': [{'externalId': 'SEVT-20240818211950-0001', 'space': 'AIM-CLK-ALL-DAT', 'reportingSite': {'externalId': 'STS-CLK', 'name': 'Clear Lake'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}, {'externalId': 'SEVT-20240923192734-0000', 'space': 'AIM-BIS-ALL-DAT', 'reportingSite': {'externalId': 'STS-BIS', 'name': 'Bishop'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}, {'externalId': 'SEVT-20250403112752-0001', 'space': 'AIM-COR-ALL-PROT', 'reportingSite': {'externalId': 'STS-CLK', 'name': 'Clear Lake'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}, {'externalId': 'SEVT-20250404101957-0001', 'space': 'AIM-CLK-ALL-DAT', 'reportingSite': {'externalId': 'STS-CLK', 'name': 'Clear Lake'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}, {'externalId': 'SEVT-20250415151441-0001', 'space': 'AIM-CLK-ALL-DAT', 'reportingSite': {'externalId': 'STS-CLK', 'name': 'Clear Lake'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}, {'externalId': 'SEVT-20250430122516-0001', 'space': 'AIM-CLK-ALL-DAT', 'reportingSite': {'externalId': 'STS-CLK', 'name': 'Clear Lake'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}, {'externalId': 'SEVT-20250522094155-0001', 'space': 'AIM-FLO-ALL-DAT', 'reportingSite': {'externalId': 'STS-FLO', 'name': 'Florence'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}, {'externalId': 'SEVT-20250527063256-0001', 'space': 'AIM-NAN-ALL-DAT', 'reportingSite': {'externalId': 'STS-NAN', 'name': 'Nanjing (Plant)'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}, {'externalId': 'SEVT-20250527083451-0001', 'space': 'AIM-NAN-ALL-DAT', 'reportingSite': {'externalId': 'STS-NAN', 'name': 'Nanjing (Plant)'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}, {'externalId': 'SEVT-20250609101014-0001', 'space': 'AIM-NAN-ALL-DAT', 'reportingSite': {'externalId': 'STS-NAN', 'name': 'Nanjing (Plant)'}, 'owner': {'externalId': '<EMAIL>', 'employeeId': '20030404', 'department': 'Information Technology, It Service Delivery', 'user': {'displayName': 'Nara, Venkata Surendra Reddy, Celanese', 'firstName': 'Venkata Surendra Reddy', 'lastName': 'Nara', 'email': '<EMAIL>', 'active': True, 'deleted': False, 'isHidden': False}, 'lanId': 'DSCVN3'}}]}}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>externalId</th>\n", "      <th>space</th>\n", "      <th>reportingSite</th>\n", "      <th>owner</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SEVT-20240818211950-0001</td>\n", "      <td>AIM-CLK-ALL-DAT</td>\n", "      <td>{'externalId': 'STS-CLK', 'name': 'Clear Lake'}</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SEVT-20240923192734-0000</td>\n", "      <td>AIM-BIS-ALL-DAT</td>\n", "      <td>{'externalId': 'STS-BIS', 'name': '<PERSON>'}</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SEVT-20250403112752-0001</td>\n", "      <td>AIM-COR-ALL-PROT</td>\n", "      <td>{'externalId': 'STS-CLK', 'name': 'Clear Lake'}</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SEVT-20250404101957-0001</td>\n", "      <td>AIM-CLK-ALL-DAT</td>\n", "      <td>{'externalId': 'STS-CLK', 'name': 'Clear Lake'}</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SEVT-20250415151441-0001</td>\n", "      <td>AIM-CLK-ALL-DAT</td>\n", "      <td>{'externalId': 'STS-CLK', 'name': 'Clear Lake'}</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>SEVT-20250430122516-0001</td>\n", "      <td>AIM-CLK-ALL-DAT</td>\n", "      <td>{'externalId': 'STS-CLK', 'name': 'Clear Lake'}</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SEVT-20250522094155-0001</td>\n", "      <td>AIM-FLO-ALL-DAT</td>\n", "      <td>{'externalId': 'STS-FLO', 'name': 'Florence'}</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>SEVT-20250527063256-0001</td>\n", "      <td>AIM-NAN-ALL-DAT</td>\n", "      <td>{'externalId': 'STS-NAN', 'name': 'Nanjing (Pl...</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>SEVT-20250527083451-0001</td>\n", "      <td>AIM-NAN-ALL-DAT</td>\n", "      <td>{'externalId': 'STS-NAN', 'name': 'Nanjing (Pl...</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>SEVT-20250609101014-0001</td>\n", "      <td>AIM-NAN-ALL-DAT</td>\n", "      <td>{'externalId': 'STS-NAN', 'name': 'Nanjing (Pl...</td>\n", "      <td>{'externalId': 'UserAzureAttribute_venkatasure...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 externalId             space  \\\n", "0  SEVT-20240818211950-0001   AIM-CLK-ALL-DAT   \n", "1  SEVT-20240923192734-0000   AIM-BIS-ALL-DAT   \n", "2  SEVT-20250403112752-0001  AIM-COR-ALL-PROT   \n", "3  SEVT-20250404101957-0001   AIM-CLK-ALL-DAT   \n", "4  SEVT-20250415151441-0001   AIM-CLK-ALL-DAT   \n", "5  SEVT-20250430122516-0001   AIM-CLK-ALL-DAT   \n", "6  SEVT-20250522094155-0001   AIM-FLO-ALL-DAT   \n", "7  SEVT-20250527063256-0001   AIM-NAN-ALL-DAT   \n", "8  SEVT-20250527083451-0001   AIM-NAN-ALL-DAT   \n", "9  SEVT-20250609101014-0001   AIM-NAN-ALL-DAT   \n", "\n", "                                       reportingSite  \\\n", "0    {'externalId': 'STS-CLK', 'name': 'Clear Lake'}   \n", "1        {'externalId': 'STS-BIS', 'name': '<PERSON>'}   \n", "2    {'externalId': 'STS-CLK', 'name': 'Clear Lake'}   \n", "3    {'externalId': 'STS-CLK', 'name': 'Clear Lake'}   \n", "4    {'externalId': 'STS-CLK', 'name': 'Clear Lake'}   \n", "5    {'externalId': 'STS-CLK', 'name': 'Clear Lake'}   \n", "6      {'externalId': 'STS-FLO', 'name': 'Florence'}   \n", "7  {'externalId': 'STS-NAN', 'name': 'Nanjing (Pl...   \n", "8  {'externalId': 'STS-NAN', 'name': 'Nanjing (Pl...   \n", "9  {'externalId': 'STS-NAN', 'name': 'Nanjing (Pl...   \n", "\n", "                                               owner  \n", "0  {'externalId': 'UserAzureAttribute_venkatasure...  \n", "1  {'externalId': 'UserAzureAttribute_venkatasure...  \n", "2  {'externalId': 'UserAzureAttribute_venkatasure...  \n", "3  {'externalId': 'UserAzureAttribute_venkatasure...  \n", "4  {'externalId': 'UserAzureAttribute_venkatasure...  \n", "5  {'externalId': 'UserAzureAttribute_venkatasure...  \n", "6  {'externalId': 'UserAzureAttribute_venkatasure...  \n", "7  {'externalId': 'UserAzureAttribute_venkatasure...  \n", "8  {'externalId': 'UserAzureAttribute_venkatasure...  \n", "9  {'externalId': 'UserAzureAttribute_venkatasure...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "from IPython.display import display\n", "\n", "try:\n", "    dataModel = 'ActionItemManagementDOM'\n", "    space = 'AIM-COR-ALL-DMD'\n", "    version = '6_6_0'\n", "\n", "    res = client.data_modeling.graphql.query(\n", "    id=(space, dataModel, version),\n", "    query=\"\"\"query listEvent {\n", "            listSourceEvent(first:1000, filter:{\n", "              and:[\n", "                {owner:{externalId:{eq:\"<EMAIL>\"}}},\n", "              ]\n", "            }){\n", "              pageInfo{\n", "                hasNextPage\n", "                hasPreviousPage\n", "                endCursor\n", "              }\n", "              items{\n", "                externalId\n", "                space\n", "                reportingSite{\n", "                  externalId\n", "                  name\n", "                }\n", "                owner{\n", "                  externalId\n", "                  employeeId\n", "                  department\n", "                  user {\n", "                    displayName\n", "                    firstName\n", "                    lastName\n", "                    email\n", "                    active\n", "                    deleted\n", "                    isHidden\n", "                  }\n", "                  lanId\n", "                }\n", "                \n", "              }\n", "            }\n", "          }\"\"\")\n", "    # print(res)\n", "    items = res['listSourceEvent']['items']\n", "    # print(items)\n", "    df = pd.DataFrame(items)\n", "    # print(df)\n", "    display(df)\n", "\n", "except Exception as e:\n", "    print(f\"Error occurred: {e}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mutation updateEvent {\n", "  upsertSourceEvent(\n", "    items: {\n", "      space: \"AIM-CLK-ALL-DAT\", \n", "      externalId: \"SEVT-20240818211950-0001\", \n", "      owner: {\n", "        node: \n", "        {\n", "          space: \"UMG-COR-ALL-DAT\", \n", "          externalId: \"<EMAIL>\"\n", "        }\n", "      }\n", "    }\n", "  ){\n", "    externalId\n", "    space\n", "    lastUpdatedTime\n", "  }\n", "}"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}