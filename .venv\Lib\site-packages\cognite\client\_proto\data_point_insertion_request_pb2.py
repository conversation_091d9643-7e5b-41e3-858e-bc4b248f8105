# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: data_point_insertion_request.proto
# Protobuf Python Version: 4.25.4
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import cognite.client._proto.data_points_pb2 as data__points__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"data_point_insertion_request.proto\x12\x1f\x63om.cognite.v1.timeseries.proto\x1a\x11\x64\x61ta_points.proto\"\xc7\x02\n\x16\x44\x61taPointInsertionItem\x12\x0c\n\x02id\x18\x01 \x01(\x03H\x00\x12\x14\n\nexternalId\x18\x02 \x01(\tH\x00\x12\x41\n\ninstanceId\x18\x05 \x01(\x0b\x32+.com.cognite.v1.timeseries.proto.InstanceIdH\x00\x12O\n\x11numericDatapoints\x18\x03 \x01(\x0b\x32\x32.com.cognite.v1.timeseries.proto.NumericDatapointsH\x01\x12M\n\x10stringDatapoints\x18\x04 \x01(\x0b\x32\x31.com.cognite.v1.timeseries.proto.StringDatapointsH\x01\x42\x15\n\x13timeSeriesReferenceB\x0f\n\rdatapointType\"c\n\x19\x44\x61taPointInsertionRequest\x12\x46\n\x05items\x18\x01 \x03(\x0b\x32\x37.com.cognite.v1.timeseries.proto.DataPointInsertionItemB\x02P\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'data_point_insertion_request_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'P\001'
  _globals['_DATAPOINTINSERTIONITEM']._serialized_start=91
  _globals['_DATAPOINTINSERTIONITEM']._serialized_end=418
  _globals['_DATAPOINTINSERTIONREQUEST']._serialized_start=420
  _globals['_DATAPOINTINSERTIONREQUEST']._serialized_end=519
# @@protoc_insertion_point(module_scope)
