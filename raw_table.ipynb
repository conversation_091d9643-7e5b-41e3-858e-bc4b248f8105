cluster = "az-eastus-1"
base_url = f"https://{cluster}.cognitedata.com"
tenant_id = "7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37"
client_id = "cb909a16-1087-4e06-9d11-8dfbd6ae1a5c"
client_secret="****************************************"
project="celanese"

from cognite.client.credentials import OAuthInteractive, OAuthClientCredentials
from cognite.client import CogniteClient, ClientConfig
import ssl
import pandas as pd
import numpy as np
from cognite.client import CogniteClient
from cognite.client.data_classes import Row

ssl.SSLContext.verify_mode = ssl.VerifyMode.CERT_OPTIONAL

credentials = OAuthClientCredentials(
  token_url=f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token",
  client_id=client_id,
  client_secret=client_secret,
  scopes=[f"{base_url}/.default"]
)
config = ClientConfig(
    client_name="Celanese",
    project=project,
    base_url=base_url,
    credentials=credentials,
)
client = CogniteClient(config)

# table_df = client.raw.rows.retrieve_dataframe("SQRE-COR-FDL-KPI", "PLANT-CASH-MARGIN", limit=-1)
# row_list = client.raw.rows.list("SQRE-COR-FDL-KPI", "PLANT-CASH-MARGIN", limit=-1)
res = client.time_series.retrieve(external_id="WWPMCFIL1:2EXT 40517")
print(res.metadata)


import pandas as pd
from IPython.display import display

try:
    dataModel = 'ActionItemManagementDOM'
    space = 'AIM-COR-ALL-DMD'
    version = '6_6_0'

    res = client.data_modeling.graphql.query(
    id=(space, dataModel, version),
    query="""query listEvent {
            listSourceEvent(first:1000, filter:{
              and:[
                {owner:{externalId:{eq:"<EMAIL>"}}},
              ]
            }){
              pageInfo{
                hasNextPage
                hasPreviousPage
                endCursor
              }
              items{
                externalId
                space
                reportingSite{
                  externalId
                  name
                }
                owner{
                  externalId
                  employeeId
                  department
                  user {
                    displayName
                    firstName
                    lastName
                    email
                    active
                    deleted
                    isHidden
                  }
                  lanId
                }
                
              }
            }
          }""")
    # print(res)
    items = res['listSourceEvent']['items']
    # print(items)
    df = pd.DataFrame(items)
    # print(df)
    display(df)

except Exception as e:
    print(f"Error occurred: {e}")


from cognite.client.data_classes.data_modeling import ViewId, aggregations as aggs
from cognite.client.data_classes.aggregations import Count
# dataModel = 'QualityDOM'
view = 'QualityNotification'
space = 'EDG-COR-ALL-DMD'
version = 'db04bce21d818f'
avg_run_time = Count("externalId")
view_id = ViewId(space, view, version)
filter= 
res = client.data_modeling.instances.aggregate(view_id, avg_run_time)
print(res)
# view_list = client.data_modeling.views.retrieve((space,view), all_versions=False)

# print(view_list)

mutation updateEvent {
  upsertSourceEvent(
    items: {
      space: "AIM-CLK-ALL-DAT", 
      externalId: "SEVT-20240818211950-0001", 
      owner: {
        node: 
        {
          space: "UMG-COR-ALL-DAT", 
          externalId: "<EMAIL>"
        }
      }
    }
  ){
    externalId
    space
    lastUpdatedTime
  }
}

