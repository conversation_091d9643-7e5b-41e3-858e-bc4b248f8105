{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["cluster = \"az-eastus-1\"\n", "base_url = f\"https://{cluster}.cognitedata.com\"\n", "tenant_id = \"7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37\"\n", "client_id = \"cb909a16-1087-4e06-9d11-8dfbd6ae1a5c\"\n", "client_secret=\"****************************************\"\n", "project=\"celanese\""]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["from cognite.client.credentials import OAuthInteractive, OAuthClientCredentials\n", "from cognite.client import CogniteClient, ClientConfig\n", "import ssl\n", "import pandas as pd\n", "import numpy as np\n", "\n", "ssl.SSLContext.verify_mode = ssl.VerifyMode.CERT_OPTIONAL"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["credentials = OAuthClientCredentials(\n", "  token_url=f\"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token\",\n", "  client_id=client_id,\n", "  client_secret=client_secret,\n", "  scopes=[f\"{base_url}/.default\"]\n", ")\n", "config = ClientConfig(\n", "    client_name=\"<PERSON><PERSON><PERSON>\",\n", "    project=project,\n", "    base_url=base_url,\n", "    credentials=credentials,\n", ")\n", "client = CogniteClient(config)"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"scrolled": true}, "outputs": [{"ename": "UnboundLocalError", "evalue": "cannot access local variable 'endCursor' where it is not associated with a value", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mUnboundLocalError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[45], line 116\u001b[0m\n\u001b[0;32m    114\u001b[0m      np_arr1 \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mconcatenate((res[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlistProductivity\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mitems\u001b[39m\u001b[38;5;124m\"\u001b[39m]))\u001b[38;5;241m.\u001b[39mtolist()\n\u001b[0;32m    115\u001b[0m      records(x\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m--> 116\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[43mrecords\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m)     \n\u001b[0;32m    117\u001b[0m np_arr1\n\u001b[0;32m    118\u001b[0m \u001b[38;5;66;03m# for x in np_arr1:\u001b[39;00m\n\u001b[0;32m    119\u001b[0m \u001b[38;5;66;03m#   print(x)\u001b[39;00m\n\u001b[0;32m    120\u001b[0m \u001b[38;5;66;03m#len(np_arr1)\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    127\u001b[0m \u001b[38;5;66;03m# arr\u001b[39;00m\n\u001b[0;32m    128\u001b[0m \u001b[38;5;66;03m#len(arr)\u001b[39;00m\n", "Cell \u001b[1;32mIn[45], line 72\u001b[0m, in \u001b[0;36mrecords\u001b[1;34m(x)\u001b[0m\n\u001b[0;32m     70\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;241m1\u001b[39m;\n\u001b[0;32m     71\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m---> 72\u001b[0m   \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mendCursor\u001b[49m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m     73\u001b[0m      res \u001b[38;5;241m=\u001b[39m client\u001b[38;5;241m.\u001b[39mdata_modeling\u001b[38;5;241m.\u001b[39mgraphql\u001b[38;5;241m.\u001b[39mquery(\n\u001b[0;32m     74\u001b[0m      \u001b[38;5;28mid\u001b[39m\u001b[38;5;241m=\u001b[39m(space, dataModel, version),\n\u001b[0;32m     75\u001b[0m          query\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\u001b[38;5;124m<PERSON><PERSON> MyQuery \u001b[39m\u001b[38;5;124m{\u001b[39m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     89\u001b[0m \u001b[38;5;124m     }\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m,\n\u001b[0;32m     90\u001b[0m      )\n\u001b[0;32m     91\u001b[0m      endCursor \u001b[38;5;241m=\u001b[39m res[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlistProductivity\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpageInfo\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mendCursor\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "\u001b[1;31mUnboundLocalError\u001b[0m: cannot access local variable 'endCursor' where it is not associated with a value"]}], "source": ["#client.transformations.jobs.list(transformation_external_id =\"tr-SAQ-COR-ALL-RMYR\", limit=-1)\n", "dataModel = \"ExecutiveKPIDOM\"\n", "space = \"EXEC-COR-ALL-DMD\"\n", "version = \"2_1_6\"\n", "hasNextPage = True\n", "endCursor = \"\"\n", "# if endCursor == \"\":\n", "#     res1 = client.data_modeling.graphql.query(\n", "#     id=(space, dataModel, version),\n", "#         query=\"\"\"query MyQuery {\n", "#       listProductivity (first:10){\n", "#         pageInfo {\n", "#           endCursor\n", "#           hasNextPage\n", "#           startCursor\n", "#           hasPreviousPage\n", "#         }\n", "#         items {\n", "#           externalId\n", "#           projectName\n", "#           businessName\n", "#         }\n", "#       }\n", "#     }\"\"\",\n", "#     )\n", "#     print(res1)\n", "# else:\n", "#     res1 = client.data_modeling.graphql.query(\n", "#     id=(space, dataModel, version),\n", "#     query=\"\"\"query MyQuery {\n", "#       listProductivity (first:10, after:\"\"){\n", "#         pageInfo {\n", "#           endCursor\n", "#           hasNextPage\n", "#           startCursor\n", "#           hasPreviousPage\n", "#         }\n", "#         items {\n", "#           externalId\n", "#           projectName\n", "#           businessName\n", "#         }\n", "#       }\n", "#     }\"\"\",\n", "#     )\n", "#     print(res1)\n", "# res2 = client.data_modeling.graphql.query(\n", "#     id=(space, dataModel, version),\n", "#     query=\"\"\"query MyQuery {\n", "#   listProductivity (first:10,\n", "#   after:\"Z0FBQUFBQm5FTndQYndMMklUVWhKQ0k2LUxycnVtRUpuYTJja0JHR2FyZk1PS05FcFBmakN1ZTg1a1RpTHpVNjFqU1NfblpiWDhlamRkRWpBalh6ZzJ2Yk9lajVzdnVsbU1EYi1CRjhYTGJGVEE2Si04Q2V2TFhLd0FKNjdueTl1and5V2lkYVJveHlSWjA0elQ1LVBVSzdiZkRPUjFrckU3Mk83VnI2NklXdXRURGRTT28wcFlyRS1aeVUtLUJvQWE5MWVkV1U4Y3dBaHZmUHl1RkZ1ejE5ejEzUzFjMnBmN0NFeURJNV9PV1QwYXpRaC00bEd4VkZLSWk4NWlwYlV0SXMxaFItTXdjMw==\"){\n", "#     pageInfo {\n", "#       endCursor\n", "#       hasNextPage\n", "#       startCursor\n", "#       hasPreviousPage\n", "#     }\n", "#     items {\n", "#       externalId\n", "#       projectName\n", "#       businessName\n", "#     }\n", "#   }\n", "# }\"\"\",\n", "# )\n", "# np_arr1 = np.concatenate((res1[\"listProductivity\"][\"items\"], res2[\"listProductivity\"][\"items\"])).tolist()\n", "# np_arr1\n", "def records(x):\n", "   if x == 2:\n", "       return 1;\n", "   else:\n", "     if endCursor == \"\":\n", "        res = client.data_modeling.graphql.query(\n", "        id=(space, dataModel, version),\n", "            query=\"\"\"query MyQuery {\n", "          listProductivity (first:10){\n", "            pageInfo {\n", "              endCursor\n", "              hasNextPage\n", "              startCursor\n", "              hasPreviousPage\n", "            }\n", "            items {\n", "              externalId\n", "              projectName\n", "              businessName\n", "            }\n", "          }\n", "        }\"\"\",\n", "        )\n", "        endCursor = res[\"listProductivity\"][\"pageInfo\"][\"endCursor\"]\n", "        # print(res)\n", "     else:\n", "        res = client.data_modeling.graphql.query(\n", "        id=(space, dataModel, version),\n", "        query=\"\"\"query MyQuery {\n", "          listProductivity (first:10, after:\"\"\"+endCursor+\"\"\"){\n", "            pageInfo {\n", "              endCursor\n", "              hasNextPage\n", "              startCursor\n", "              hasPreviousPage\n", "            }\n", "            items {\n", "              externalId\n", "              projectName\n", "              businessName\n", "            }\n", "          }\n", "        }\"\"\",\n", "        )\n", "        endCursor = res[\"listProductivity\"][\"pageInfo\"][\"endCursor\"]\n", "        # print(res) \n", "     np_arr1 = np.concatenate((res[\"listProductivity\"][\"items\"])).tolist()\n", "     records(x+1)\n", "y = 0       \n", "print(records(y))     \n", "np_arr1\n", "# for x in np_arr1:\n", "#   print(x)\n", "#len(np_arr1)\n", "# len(arr)\n", "#len(res2[\"listProductivity\"][\"items\"])\n", "# res2[\"listProductivity\"][\"items\"]\n", "# arr.extend(res1[\"listProductivity\"][\"items\"])\n", "# arr.extend(res2[\"listProductivity\"][\"items\"])\n", "# res[\"listProductivity\"][\"pageInfo\"][\"hasNextPage\"]\n", "# arr\n", "#len(arr)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>externalId</th>\n", "      <th>projectName</th>\n", "      <th>businessName</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>PRDTY-BC296586-6E3D-4A83-9187-D1DF639D39A6</td>\n", "      <td>ES:Replacing hazardous waste disposal provider...</td>\n", "      <td>Others/Shared</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>PRDTY-88857351-84D8-46DE-A861-E7106A635EFF</td>\n", "      <td>Fusel oil sell to reduce WW cost-Sep. 2014</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PRDTY-767660E5-EF23-4B12-96B1-A07D881CA3DB</td>\n", "      <td>Freight Saving for Bulk Shipments from Nanjing...</td>\n", "      <td>Hac</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PRDTY-73131B45-6F01-411C-AC04-BB58B5B237CD</td>\n", "      <td>Color NPEF - CX 2002-2 ED4858</td>\n", "      <td>Compounding</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>PRDTY-3F617C35-1AE8-4933-861F-15509B87D3D6</td>\n", "      <td>CN: Increase acrylate capacity</td>\n", "      <td>Conventional</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>PRDTY-88F25BC8-6DDE-4F03-83C0-DC9C78D280A9</td>\n", "      <td>VA: Implementation of Barbados - maintenance cost</td>\n", "      <td>VAM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>PRDTY-BFB913E1-FF2F-430B-A013-FA1A5D61302E</td>\n", "      <td>Testing &amp; Analytical Service</td>\n", "      <td>Others/Shared</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>PRDTY-7FF76624-C4D9-4353-A0C0-0F959DB833F3</td>\n", "      <td>MEG - cost reduction 2021</td>\n", "      <td>Others/Shared</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>PRDTY-62047D9A-3777-4C1E-80E9-9BD856BA6E71</td>\n", "      <td>2021 Hongkong office leasing</td>\n", "      <td>Others/Shared</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>PRDTY-259A7342-76B6-404D-8B1C-6C8EAC58B34F</td>\n", "      <td>VAE: new coil reactor R7 Step 1</td>\n", "      <td>VAE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>PRDTY-445232B7-D15A-4F97-A11C-A1520DE5F522</td>\n", "      <td>Ethylene Spot Shipments for Nanjing plant</td>\n", "      <td>Others/Shared</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>PRDTY-C5567FEC-90F4-4F30-8F55-621A0A1251EF</td>\n", "      <td>Improved Steam Balance</td>\n", "      <td>Utilities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>PRDTY-D7BA3AF9-98E7-4618-B16E-B9A498FAAADA</td>\n", "      <td>Water Cost saving</td>\n", "      <td>N/A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>PRDTY-C80E289E-7763-47F1-B98F-F7061B44054D</td>\n", "      <td>March 2021 Pd Purchase</td>\n", "      <td>VAM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>PRDTY-35050FE1-C92A-46B6-8011-6DAE37D998A1</td>\n", "      <td>External Service Reduction - 2</td>\n", "      <td>POM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>PRDTY-A797EAF8-67C8-4B0A-A006-DCE2DBF47140</td>\n", "      <td>VE: Replace cooling water pump motor  to save ...</td>\n", "      <td>VAE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>PRDTY-5495FD46-3E08-47C4-B053-F52642C3BA98</td>\n", "      <td>Reduced maintenance contractor spend</td>\n", "      <td>N/A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>PRDTY-18A8A8D5-B015-4199-8EDB-FD198478DC9F</td>\n", "      <td>VA: Replacement of P5010</td>\n", "      <td>VAM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>PRDTY-6919ED55-5A03-4276-A633-ED88CCC42D24</td>\n", "      <td>MOB: VAE Flexible conveying pipline for PVOH (...</td>\n", "      <td>VAE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>PRDTY-9B5B6B97-48AD-458F-88A8-1E2CDF6C348F</td>\n", "      <td>Reduktion Nachlaufzeiten S&amp;L <PERSON>ner</td>\n", "      <td>Compounding</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    externalId  \\\n", "0   PRDTY-BC296586-6E3D-4A83-9187-D1DF639D39A6   \n", "1   PRDTY-88857351-84D8-46DE-A861-E7106A635EFF   \n", "2   PRDTY-767660E5-EF23-4B12-96B1-A07D881CA3DB   \n", "3   PRDTY-73131B45-6F01-411C-AC04-BB58B5B237CD   \n", "4   PRDTY-3F617C35-1AE8-4933-861F-15509B87D3D6   \n", "5   PRDTY-88F25BC8-6DDE-4F03-83C0-DC9C78D280A9   \n", "6   PRDTY-BFB913E1-FF2F-430B-A013-FA1A5D61302E   \n", "7   PRDTY-7FF76624-C4D9-4353-A0C0-0F959DB833F3   \n", "8   PRDTY-62047D9A-3777-4C1E-80E9-9BD856BA6E71   \n", "9   PRDTY-259A7342-76B6-404D-8B1C-6C8EAC58B34F   \n", "10  PRDTY-445232B7-D15A-4F97-A11C-A1520DE5F522   \n", "11  PRDTY-C5567FEC-90F4-4F30-8F55-621A0A1251EF   \n", "12  PRDTY-D7BA3AF9-98E7-4618-B16E-B9A498FAAADA   \n", "13  PRDTY-C80E289E-7763-47F1-B98F-F7061B44054D   \n", "14  PRDTY-35050FE1-C92A-46B6-8011-6DAE37D998A1   \n", "15  PRDTY-A797EAF8-67C8-4B0A-A006-DCE2DBF47140   \n", "16  PRDTY-5495FD46-3E08-47C4-B053-F52642C3BA98   \n", "17  PRDTY-18A8A8D5-B015-4199-8EDB-FD198478DC9F   \n", "18  PRDTY-6919ED55-5A03-4276-A633-ED88CCC42D24   \n", "19  PRDTY-9B5B6B97-48AD-458F-88A8-1E2CDF6C348F   \n", "\n", "                                          projectName   businessName  \n", "0   ES:Replacing hazardous waste disposal provider...  Others/Shared  \n", "1          Fusel oil sell to reduce WW cost-Sep. 2014        Ethanol  \n", "2   Freight Saving for Bulk Shipments from Nanjing...            Hac  \n", "3                       Color NPEF - CX 2002-2 ED4858    Compounding  \n", "4                     CN: Increase acrylate capacity    Conventional  \n", "5   VA: Implementation of Barbados - maintenance cost            VAM  \n", "6                        Testing & Analytical Service  Others/Shared  \n", "7                           MEG - cost reduction 2021  Others/Shared  \n", "8                        2021 Hongkong office leasing  Others/Shared  \n", "9                     VAE: new coil reactor R7 Step 1            VAE  \n", "10          Ethylene Spot Shipments for Nanjing plant  Others/Shared  \n", "11                             Improved Steam Balance      Utilities  \n", "12                                  Water Cost saving            N/A  \n", "13                             March 2021 Pd Purchase            VAM  \n", "14                     External Service Reduction - 2            POM  \n", "15  VE: Replace cooling water pump motor  to save ...            VAE  \n", "16               Reduced maintenance contractor spend            N/A  \n", "17                           VA: Replacement of P5010            VAM  \n", "18  MOB: VAE Flexible conveying pipline for PVOH (...            VAE  \n", "19              Reduktion Nachlaufzeiten S&L Trockner    Compounding  "]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(arr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}